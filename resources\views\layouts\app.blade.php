<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" data-theme="light">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>@yield('title', config('app.name', 'Laravel'))</title>
    
    <!-- CSS files -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@latest/dist/css/tabler.min.css" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons-sprite.svg" rel="stylesheet"/>
    
    <!-- Custom CSS -->
    <style>
        /* Light mode variables (default) */
        :root {
            --tblr-body-bg: #ffffff;
            --tblr-body-color: #1e293b;
            --tblr-card-bg: #ffffff;
            --tblr-border-color: #e2e8f0;
            --tblr-navbar-bg: #ffffff;
            --tblr-navbar-color: #1e293b;
            --tblr-sidebar-bg: #f8fafc;
            --tblr-sidebar-color: #475569;
            --tblr-text-muted: #64748b;
            --tblr-btn-bg: #f1f5f9;
            --tblr-btn-color: #334155;
            --tblr-input-bg: #ffffff;
            --tblr-input-border: #d1d5db;
            --tblr-shadow: rgba(0, 0, 0, 0.1);
        }

        /* Dark mode styles */
        [data-theme="dark"] {
            --tblr-body-bg: #0f172a;
            --tblr-body-color: #e2e8f0;
            --tblr-card-bg: #1e293b;
            --tblr-border-color: #334155;
            --tblr-navbar-bg: #1e293b;
            --tblr-navbar-color: #e2e8f0;
            --tblr-sidebar-bg: #0f172a;
            --tblr-sidebar-color: #cbd5e1;
            --tblr-text-muted: #94a3b8;
            --tblr-btn-bg: #334155;
            --tblr-btn-color: #e2e8f0;
            --tblr-input-bg: #1e293b;
            --tblr-input-border: #475569;
            --tblr-shadow: rgba(0, 0, 0, 0.3);
        }

        /* Apply theme variables to elements */
        body {
            background-color: var(--tblr-body-bg) !important;
            color: var(--tblr-body-color) !important;
        }

        .navbar {
            background-color: var(--tblr-navbar-bg) !important;
            color: var(--tblr-navbar-color) !important;
            border-bottom: 1px solid var(--tblr-border-color) !important;
        }

        .navbar .navbar-brand,
        .navbar .nav-link {
            color: var(--tblr-navbar-color) !important;
        }

        .card {
            background-color: var(--tblr-card-bg) !important;
            border-color: var(--tblr-border-color) !important;
            box-shadow: 0 1px 3px var(--tblr-shadow) !important;
        }

        .text-muted {
            color: var(--tblr-text-muted) !important;
        }

        .btn-outline-primary,
        .btn-outline-secondary,
        .btn-outline-info {
            background-color: var(--tblr-btn-bg);
            color: var(--tblr-btn-color);
            border-color: var(--tblr-border-color);
        }

        .btn-outline-primary:hover,
        .btn-outline-secondary:hover,
        .btn-outline-info:hover {
            background-color: var(--tblr-border-color);
        }

        .form-control {
            background-color: var(--tblr-input-bg) !important;
            border-color: var(--tblr-input-border) !important;
            color: var(--tblr-body-color) !important;
        }

        .form-control:focus {
            background-color: var(--tblr-input-bg) !important;
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25) !important;
        }

        /* Theme transition */
        * {
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
        }

        /* Theme toggle button */
        .theme-toggle {
            cursor: pointer;
            border: none;
            background: none;
            padding: 0.5rem;
            border-radius: 0.375rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--tblr-navbar-color);
        }

        .theme-toggle:hover {
            background-color: var(--tblr-btn-bg);
        }

        /* Dynamic icon visibility */
        .theme-icon-light {
            display: block;
        }

        .theme-icon-dark {
            display: none;
        }

        [data-theme="dark"] .theme-icon-light {
            display: none;
        }

        [data-theme="dark"] .theme-icon-dark {
            display: block;
        }

        /* Additional dark mode styling for UI elements */
        .page-header {
            background-color: var(--tblr-body-bg) !important;
            border-bottom: 1px solid var(--tblr-border-color) !important;
        }

        .page-title {
            color: var(--tblr-body-color) !important;
        }

        .page-pretitle {
            color: var(--tblr-text-muted) !important;
        }

        .avatar {
            border: 2px solid var(--tblr-border-color) !important;
        }

        .badge {
            border: 1px solid var(--tblr-border-color);
        }

        .list-unstyled li {
            color: var(--tblr-body-color) !important;
        }

        .font-weight-medium {
            color: var(--tblr-body-color) !important;
        }

        /* Fix for SVG icons in dark mode */
        .icon {
            color: inherit;
        }

        /* Dropdown and navigation improvements */
        .dropdown-menu {
            background-color: var(--tblr-card-bg) !important;
            border-color: var(--tblr-border-color) !important;
        }

        .dropdown-item {
            color: var(--tblr-body-color) !important;
        }

        .dropdown-item:hover {
            background-color: var(--tblr-btn-bg) !important;
        }

        /* Table styling for dark mode */
        .table {
            color: var(--tblr-body-color) !important;
        }

        .table th,
        .table td {
            border-color: var(--tblr-border-color) !important;
        }

        /* Alert styling */
        .alert {
            border-color: var(--tblr-border-color) !important;
        }

        /* Breadcrumb styling */
        .breadcrumb {
            background-color: transparent !important;
        }

        .breadcrumb-item a {
            color: var(--tblr-text-muted) !important;
        }

        .breadcrumb-item.active {
            color: var(--tblr-body-color) !important;
        }

        /* Modal styling */
        .modal-content {
            background-color: var(--tblr-card-bg) !important;
            border-color: var(--tblr-border-color) !important;
        }

        .modal-header {
            border-bottom-color: var(--tblr-border-color) !important;
        }

        .modal-footer {
            border-top-color: var(--tblr-border-color) !important;
        }

        /* Pagination styling */
        .page-link {
            background-color: var(--tblr-card-bg) !important;
            border-color: var(--tblr-border-color) !important;
            color: var(--tblr-body-color) !important;
        }

        .page-link:hover {
            background-color: var(--tblr-btn-bg) !important;
            border-color: var(--tblr-border-color) !important;
        }

        .page-item.active .page-link {
            background-color: #3b82f6 !important;
            border-color: #3b82f6 !important;
        }

        /* Progress bar styling */
        .progress {
            background-color: var(--tblr-btn-bg) !important;
        }

        /* List group styling */
        .list-group-item {
            background-color: var(--tblr-card-bg) !important;
            border-color: var(--tblr-border-color) !important;
            color: var(--tblr-body-color) !important;
        }

        /* Tooltip and popover styling */
        .tooltip-inner {
            background-color: var(--tblr-card-bg) !important;
            color: var(--tblr-body-color) !important;
        }

        .popover {
            background-color: var(--tblr-card-bg) !important;
            border-color: var(--tblr-border-color) !important;
        }

        .popover-body {
            color: var(--tblr-body-color) !important;
        }
        
        /* Hide icons based on theme */
        [data-theme="light"] .theme-icon-dark,
        [data-theme="dark"] .theme-icon-light {
            display: none;
        }
    </style>
    
    @stack('styles')
</head>
<body>
    <div class="page">
        <!-- Navbar -->
        <header class="navbar navbar-expand-md navbar-light d-print-none">
            <div class="container-xl">
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu" aria-controls="navbar-menu" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
                    <a href="{{ url('/') }}">
                        {{ config('app.name', 'Laravel') }}
                    </a>
                </h1>

                <div class="collapse navbar-collapse" id="navbar-menu">
                    <div class="d-flex flex-column flex-md-row flex-fill align-items-stretch align-items-md-center">
                        <ul class="navbar-nav">
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url('/') }}">
                                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                                        <svg class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                            <polyline points="5 12 3 12 12 3 21 12 19 12"></polyline>
                                            <path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7"></path>
                                            <path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6"></path>
                                        </svg>
                                    </span>
                                    <span class="nav-link-title">Home</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url('/tabler') }}">
                                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                                        <svg class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                            <rect x="4" y="4" width="6" height="6" rx="1"></rect>
                                            <rect x="14" y="4" width="6" height="6" rx="1"></rect>
                                            <rect x="4" y="14" width="6" height="6" rx="1"></rect>
                                            <rect x="14" y="14" width="6" height="6" rx="1"></rect>
                                        </svg>
                                    </span>
                                    <span class="nav-link-title">Dashboard</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url('/components') }}">
                                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                                        <svg class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                            <rect x="4" y="4" width="16" height="16" rx="2"></rect>
                                            <rect x="9" y="9" width="6" height="6"></rect>
                                            <line x1="9" y1="1" x2="9" y2="4"></line>
                                            <line x1="15" y1="1" x2="15" y2="4"></line>
                                            <line x1="9" y1="20" x2="9" y2="23"></line>
                                            <line x1="15" y1="20" x2="15" y2="23"></line>
                                            <line x1="20" y1="9" x2="23" y2="9"></line>
                                            <line x1="20" y1="14" x2="23" y2="14"></line>
                                            <line x1="1" y1="9" x2="4" y2="9"></line>
                                            <line x1="1" y1="14" x2="4" y2="14"></line>
                                        </svg>
                                    </span>
                                    <span class="nav-link-title">Components</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="navbar-nav flex-row order-md-last">
                    <!-- Theme Toggle -->
                    <div class="nav-item">
                        <button class="theme-toggle" onclick="toggleTheme()" title="Toggle theme">
                            <!-- Light mode icon (sun) - shown in light mode -->
                            <svg class="theme-icon-light" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="5"></circle>
                                <line x1="12" y1="1" x2="12" y2="3"></line>
                                <line x1="12" y1="21" x2="12" y2="23"></line>
                                <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                                <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                                <line x1="1" y1="12" x2="3" y2="12"></line>
                                <line x1="21" y1="12" x2="23" y2="12"></line>
                                <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                                <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                            </svg>
                            <!-- Dark mode icon (moon) - shown in dark mode -->
                            <svg class="theme-icon-dark" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z"></path>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-link-icon d-md-none d-lg-inline-block">
                                <svg class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M9 7 h-3a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-3"></path>
                                    <path d="M9 15h3l8.5 -8.5a1.5 1.5 0 0 0 -3 -3l-8.5 8.5v3"></path>
                                    <path d="M16 5l3 3"></path>
                                </svg>
                            </span>
                            <span class="nav-link-title">Demo User</span>
                        </a>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Page wrapper -->
        <div class="page-wrapper">
            <!-- Page header -->
            @hasSection('page-header')
            <div class="page-header d-print-none">
                <div class="container-xl">
                    <div class="row g-2 align-items-center">
                        <div class="col">
                            @yield('page-header')
                        </div>
                    </div>
                </div>
            </div>
            @endif
            
            <!-- Page body -->
            <div class="page-body">
                <div class="container-xl">
                    @if(session('success'))
                    <div class="alert alert-success alert-dismissible" role="alert">
                        <div class="d-flex">
                            <div>
                                <svg class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M5 12l5 5l10 -10"></path>
                                </svg>
                            </div>
                            <div>{{ session('success') }}</div>
                        </div>
                        <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                    </div>
                    @endif
                    
                    @if(session('error'))
                    <div class="alert alert-danger alert-dismissible" role="alert">
                        <div class="d-flex">
                            <div>
                                <svg class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M12 9v2m0 4v.01"></path>
                                    <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"></path>
                                </svg>
                            </div>
                            <div>{{ session('error') }}</div>
                        </div>
                        <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                    </div>
                    @endif
                    
                    @yield('content')
                </div>
            </div>
            
            <!-- Page footer -->
            <footer class="footer footer-transparent d-print-none">
                <div class="container-xl">
                    <div class="row text-center align-items-center flex-row-reverse">
                        <div class="col-lg-auto ms-lg-auto">
                            <ul class="list-inline list-inline-dots mb-0">
                                <li class="list-inline-item">
                                    <a href="https://tabler.io" target="_blank" class="link-secondary">
                                        Powered by Tabler
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class="col-12 col-lg-auto mt-3 mt-lg-0">
                            <ul class="list-inline list-inline-dots mb-0">
                                <li class="list-inline-item">
                                    Copyright &copy; {{ date('Y') }}
                                    <a href="{{ url('/') }}" class="link-secondary">{{ config('app.name') }}</a>.
                                    All rights reserved.
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>
    
    <!-- Tabler Core -->
    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@latest/dist/js/tabler.min.js"></script>
    
    <!-- Theme Toggle Script -->
    <script>
        // Theme management with enhanced functionality
        function getStoredTheme() {
            return localStorage.getItem('theme') || 'light';
        }

        function setStoredTheme(theme) {
            localStorage.setItem('theme', theme);
        }

        function setTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            setStoredTheme(theme);

            // Update button title
            const toggleButton = document.querySelector('.theme-toggle');
            if (toggleButton) {
                toggleButton.title = theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode';
            }

            // Dispatch custom event for other components to listen
            window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
        }

        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            setTheme(newTheme);

            // Add visual feedback
            const toggleButton = document.querySelector('.theme-toggle');
            if (toggleButton) {
                toggleButton.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    toggleButton.style.transform = 'scale(1)';
                }, 150);
            }
        }

        // Initialize theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            const storedTheme = getStoredTheme();

            // Check if user has system preference and no stored theme
            if (!localStorage.getItem('theme') && window.matchMedia) {
                const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                setTheme(prefersDark ? 'dark' : 'light');
            } else {
                setTheme(storedTheme);
            }
        });

        // System theme detection with improved handling
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

            // Use modern addEventListener if available
            if (mediaQuery.addEventListener) {
                mediaQuery.addEventListener('change', function(e) {
                    if (!localStorage.getItem('theme')) {
                        setTheme(e.matches ? 'dark' : 'light');
                    }
                });
            } else {
                // Fallback for older browsers
                mediaQuery.addListener(function(e) {
                    if (!localStorage.getItem('theme')) {
                        setTheme(e.matches ? 'dark' : 'light');
                    }
                });
            }
        }

        // Keyboard shortcut for theme toggle (Ctrl/Cmd + Shift + T)
        document.addEventListener('keydown', function(e) {
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                toggleTheme();
            }
        });
    </script>
    
    @stack('scripts')
</body>
</html>
